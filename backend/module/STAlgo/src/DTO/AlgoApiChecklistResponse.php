<?php

namespace STAlgo\DTO;

class AlgoApiChecklistResponse
{
    private array $checklistResult;

    public function __construct(
        public string $status,
        array $results,
    ) {
        $this->checklistResult = array_change_key_case(
            array_combine(
                array_column($results['checklist_result'], 'title'),
                $results['checklist_result']
            )
        );
    }

    public function isSuccessful(): bool
    {
        return $this->status === 'ok';
    }

    public function getChecklistResult(): array
    {
        return $this->checklistResult;
    }

    public function getChecklistPointResult(string $checklistPointTitle): ?array
    {
        return $this->getChecklistResult()[strtolower($checklistPointTitle)] ?? null;
    }
}
