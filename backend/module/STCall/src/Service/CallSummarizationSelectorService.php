<?php

declare(strict_types=1);

namespace STCall\Service;

use STCall\Data\CallSummarizationRepository;

final class CallSummarizationSelectorService
{
    public function __construct(private readonly CallSummarizationRepository $callSummarizationRepository)
    {
    }

    /**
     * @param string $callId
     * @param int $companyId
     * @return array
     */
    public function getCallSummarization(string $callId, int $companyId): array
    {
        return $this->callSummarizationRepository->getExtendedSummarizationData($callId, $companyId);
    }
}
